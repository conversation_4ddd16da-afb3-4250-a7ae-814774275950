/**
 * Production Test Suite - Real World Scenario Simulation
 *
 * This test simulates the complete production workflow from subtitle extraction
 * through translation, testing all tools with the first 60 lines of text.
 *
 * Test Flow:
 * 1. Create mock MKV file structure
 * 2. Simulate subtitle extraction (English + French)
 * 3. Run scene detection and context analysis
 * 4. Execute translation with all tools (first 60 lines only)
 * 5. Test second language validation
 * 6. Test examples reference
 * 7. Test metadata persistence
 * 8. Generate comprehensive report
 *
 * Usage: node app/production-test.js
 */

// Load environment variables from .env file
import { config } from 'dotenv';
config();

import fs from 'fs';
import path from 'path';
import { Claude4Translator } from './2translate/claude4-translator.js';
import { SecondLanguageValidator } from './2translate/tools/second-language-validator.js';
import { ExamplesReference } from './2translate/tools/examples-reference.js';
import { MetadataPersistence } from './2translate/tools/metadata-persistence.js';

// Color constants for better log readability
const COLORS = {
  RESET: '\x1b[0m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  RED: '\x1b[31m',
  GRAY: '\x1b[90m',
  SUCCESS: '\x1b[32m\x1b[1m',
  WARNING: '\x1b[33m\x1b[1m',
  ERROR: '\x1b[31m\x1b[1m',
  INFO: '\x1b[36m',
  TEST: '\x1b[35m\x1b[1m',
  PRODUCTION: '\x1b[36m\x1b[1m'
};

class ProductionTest {
  constructor() {
    this.testResults = {
      subtitleExtraction: false,
      sceneDetection: false,
      translation: false,
      secondLanguageValidation: false,
      examplesReference: false,
      metadataPersistence: false,
      overallSuccess: false
    };

    this.testData = {
      animeTitle: "Test Anime Series",
      episode: "01",
      fileName: "test_anime_01_eng.txt",
      sampleLines: 60
    };

    // Detailed error tracking
    this.detailedErrors = {
      subtitleExtraction: [],
      sceneDetection: [],
      translation: [],
      secondLanguageValidation: [],
      examplesReference: [],
      metadataPersistence: []
    };

    this.startTime = Date.now();
  }

  /**
   * Capture detailed error information
   * @param {string} testName - Name of the test that failed
   * @param {Error} error - The error object
   * @param {Object} context - Additional context information
   */
  captureDetailedError(testName, error, context = {}) {
    const errorInfo = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      context: context,
      type: error.constructor.name
    };

    if (this.detailedErrors[testName]) {
      this.detailedErrors[testName].push(errorInfo);
    }

    console.error(`${COLORS.ERROR}💥 [Production Test] Detailed error in ${testName}:${COLORS.RESET}`);
    console.error(`${COLORS.ERROR}   Message: ${error.message}${COLORS.RESET}`);
    console.error(`${COLORS.ERROR}   Type: ${error.constructor.name}${COLORS.RESET}`);
    if (context && Object.keys(context).length > 0) {
      console.error(`${COLORS.ERROR}   Context: ${JSON.stringify(context, null, 2)}${COLORS.RESET}`);
    }
    console.error(`${COLORS.GRAY}   Stack: ${error.stack}${COLORS.RESET}`);
  }

  /**
   * Run the complete production test suite
   */
  async runProductionTest() {
    console.log(`${COLORS.PRODUCTION}🚀 [Production Test] Starting REAL TRANSLATION CYCLE test...${COLORS.RESET}`);
    console.log(`${COLORS.INFO}📋 [Production Test] Testing complete workflow with ${this.testData.sampleLines} lines from ${this.testData.animeTitle} Episode ${this.testData.episode}${COLORS.RESET}`);
    console.log(`${COLORS.WARNING}⚠️  [Production Test] This will perform ACTUAL translation operations, not simulations${COLORS.RESET}`);

    // Verify API key is loaded
    if (!process.env.ANTHROPIC_API_KEY) {
      console.error(`${COLORS.ERROR}❌ [Production Test] ANTHROPIC_API_KEY not found in environment variables${COLORS.RESET}`);
      console.error(`${COLORS.ERROR}   Please ensure the .env file exists and contains ANTHROPIC_API_KEY${COLORS.RESET}`);
      throw new Error('Missing ANTHROPIC_API_KEY environment variable');
    } else {
      const keyPreview = process.env.ANTHROPIC_API_KEY.substring(0, 12) + '...';
      console.log(`${COLORS.SUCCESS}✅ [Production Test] API key loaded: ${keyPreview}${COLORS.RESET}`);
    }

    try {
      // Step 1: Setup test environment
      await this.setupTestEnvironment();

      // Step 2: Test subtitle extraction (real file operations)
      await this.testSubtitleExtraction();

      // Step 3: Test complete translation cycle
      await this.testCompleteTranslationCycle();

      // Step 4: Test individual tool components
      await this.testSecondLanguageValidation();
      await this.testExamplesReference();
      await this.testMetadataPersistence();

      // Step 5: Generate final report
      await this.generateReport();

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Production Test] Critical error: ${error.message}${COLORS.RESET}`);
      this.captureDetailedError('critical', error, { phase: 'main_execution' });
      this.testResults.overallSuccess = false;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Setup test environment and extract real subtitles
   */
  async setupTestEnvironment() {
    console.log(`${COLORS.TEST}🔧 [Production Test] Setting up test environment with REAL subtitle extraction...${COLORS.RESET}`);

    // Find available MKV files in downloads directory
    const downloadsDir = '0rss/downloads';
    const mkvFiles = fs.readdirSync(downloadsDir).filter(file => file.endsWith('.mkv'));

    if (mkvFiles.length === 0) {
      throw new Error('No MKV files found in downloads directory for subtitle extraction');
    }

    // Use the first available MKV file
    const selectedMkvFile = mkvFiles[0];
    const mkvPath = path.join(downloadsDir, selectedMkvFile);

    console.log(`${COLORS.INFO}📁 [Production Test] Found MKV file: ${selectedMkvFile}${COLORS.RESET}`);
    console.log(`${COLORS.INFO}🎬 [Production Test] Extracting subtitles from: ${mkvPath}${COLORS.RESET}`);

    // Update test data to use the actual file
    const baseFileName = selectedMkvFile.slice(0, -4); // Remove .mkv extension
    this.testData.fileName = `${baseFileName}_eng.txt`;
    this.testData.actualMkvFile = selectedMkvFile;
    this.testData.mkvPath = mkvPath;

    // Extract anime title from filename for better metadata
    const titleMatch = selectedMkvFile.match(/\]\s(.*?)\s-\s(\d+)/);
    if (titleMatch) {
      this.testData.animeTitle = titleMatch[1];
      this.testData.episode = titleMatch[2].padStart(2, '0');
    }

    console.log(`${COLORS.INFO}📺 [Production Test] Detected anime: ${this.testData.animeTitle} Episode ${this.testData.episode}${COLORS.RESET}`);
    console.log(`${COLORS.SUCCESS}✅ [Production Test] Test environment setup complete${COLORS.RESET}`);
  }

  /**
   * Generate sample subtitle content for testing
   */
  generateSampleSubtitleContent(language) {
    const englishLines = [
      "Narrator | Welcome to the world of magic and adventure.",
      "Protagonist | I never thought I'd see a place like this!",
      "Deuteragonist | This is just the beginning of our journey.",
      "Protagonist | What should we do first?",
      "Mentor | First, you must learn to control your power.",
      "Protagonist | I'm ready to learn whatever it takes.",
      "Antagonist | You think you can defeat me so easily?",
      "Protagonist | I won't give up, no matter what!",
      "Friend | We believe in you! You can do this!",
      "Protagonist | Thank you, everyone. Let's do this together!",
      "Narrator | And so their adventure truly began...",
      "Protagonist | This magic feels incredible!",
      "Mentor | Remember, with great power comes great responsibility.",
      "Deuteragonist | I'll always be by your side.",
      "Protagonist | I promise to protect everyone I care about.",
      "Villain | Your determination is admirable, but futile.",
      "Protagonist | I'll prove you wrong!",
      "Friend | The power of friendship will guide us!",
      "Protagonist | Together, we're unstoppable!",
      "Narrator | Their bonds grew stronger with each challenge."
    ];

    const frenchLines = [
      "Narrateur | Bienvenue dans le monde de la magie et de l'aventure.",
      "Protagoniste | Je n'aurais jamais pensé voir un endroit comme celui-ci !",
      "Deutéragoniste | Ce n'est que le début de notre voyage.",
      "Protagoniste | Que devrions-nous faire en premier ?",
      "Mentor | D'abord, tu dois apprendre à contrôler ton pouvoir.",
      "Protagoniste | Je suis prêt à apprendre tout ce qu'il faut.",
      "Antagoniste | Tu penses pouvoir me vaincre si facilement ?",
      "Protagoniste | Je n'abandonnerai pas, quoi qu'il arrive !",
      "Ami | Nous croyons en toi ! Tu peux le faire !",
      "Protagoniste | Merci à tous. Faisons cela ensemble !",
      "Narrateur | Et ainsi leur aventure commença vraiment...",
      "Protagoniste | Cette magie est incroyable !",
      "Mentor | Souviens-toi, un grand pouvoir implique de grandes responsabilités.",
      "Deutéragoniste | Je serai toujours à tes côtés.",
      "Protagoniste | Je promets de protéger tous ceux qui me sont chers.",
      "Méchant | Ta détermination est admirable, mais futile.",
      "Protagoniste | Je vais te prouver le contraire !",
      "Ami | Le pouvoir de l'amitié nous guidera !",
      "Protagoniste | Ensemble, nous sommes imbattables !",
      "Narrateur | Leurs liens se renforcèrent à chaque défi."
    ];

    const lines = language === 'french' ? frenchLines : englishLines;
    
    // Repeat and extend to reach 60 lines
    const extendedLines = [];
    for (let i = 0; i < this.testData.sampleLines; i++) {
      const lineIndex = i % lines.length;
      const lineNumber = Math.floor(i / lines.length) + 1;
      const suffix = lineNumber > 1 ? ` (${lineNumber})` : '';
      extendedLines.push(lines[lineIndex] + suffix);
    }
    
    return extendedLines.join('\n');
  }

  /**
   * Test subtitle extraction with real MKV file processing
   */
  async testSubtitleExtraction() {
    console.log(`${COLORS.TEST}📄 [Production Test] Testing REAL subtitle extraction from MKV file...${COLORS.RESET}`);

    try {
      console.log(`${COLORS.INFO}🎬 [Production Test] Running clear.js process to extract subtitles from: ${this.testData.actualMkvFile}${COLORS.RESET}`);
      console.log(`${COLORS.INFO}🔧 [Production Test] Setting test mode to prevent automatic translation launch and Discord notifications${COLORS.RESET}`);

      // Run the clear.js process to extract and process subtitles
      const { spawn } = await import('child_process');

      const clearProcess = spawn('node', ['1clear/clear.js'], {
        stdio: 'pipe',
        shell: true,
        env: {
          ...process.env,
          PRODUCTION_TEST_MODE: 'true'  // Set test mode to prevent auto-launching translation
        }
      });

      let stdout = '';
      let stderr = '';

      clearProcess.stdout.on('data', (data) => {
        stdout += data.toString();
        // Show real-time output
        process.stdout.write(data);
      });

      clearProcess.stderr.on('data', (data) => {
        stderr += data.toString();
        // Show real-time errors
        process.stderr.write(data);
      });

      // Wait for the process to complete
      const exitCode = await new Promise((resolve) => {
        clearProcess.on('close', resolve);
      });

      if (exitCode !== 0) {
        throw new Error(`Clear.js process failed with exit code ${exitCode}. Stderr: ${stderr}`);
      }

      console.log(`${COLORS.SUCCESS}✅ [Production Test] Clear.js process completed successfully${COLORS.RESET}`);

      const baseFileName = this.testData.actualMkvFile.slice(0, -4);
      const outputPathEng = `1clear/extracted/${baseFileName}_eng.ass`;
      const outputPathOther = `1clear/extracted/${baseFileName}_other.ass`;

      console.log(`${COLORS.INFO}🎬 [Production Test] Extracting English subtitles from: ${this.testData.mkvPath}${COLORS.RESET}`);

      // Subtitles already extracted by clear.js process
      console.log(`${COLORS.SUCCESS}✅ [Production Test] English subtitles extracted to: ${outputPathEng}${COLORS.RESET}`);

      // Try to extract French subtitles for second language validation
      console.log(`${COLORS.INFO}🇫🇷 [Production Test] Attempting to extract French subtitles...${COLORS.RESET}`);
      try {
        // French subtitles already extracted by clear.js process
        console.log(`${COLORS.SUCCESS}✅ [Production Test] French subtitles extracted to: ${outputPathOther}${COLORS.RESET}`);
      } catch (frenchError) {
        console.warn(`${COLORS.WARNING}⚠️  [Production Test] Failed to extract French subtitles: ${frenchError.message}${COLORS.RESET}`);
        console.log(`${COLORS.INFO}� [Production Test] Continuing without French subtitles - second language validation will be limited${COLORS.RESET}`);
      }

      // Clear the ASS files to extract dialogue lines
      console.log(`${COLORS.INFO}🧹 [Production Test] Processing English subtitles...${COLORS.RESET}`);
      // English subtitles already processed by clear.js

      if (fs.existsSync(outputPathOther)) {
        console.log(`${COLORS.INFO}🧹 [Production Test] Processing French subtitles...${COLORS.RESET}`);
        // French subtitles already processed by clear.js
      }

      // Verify the extracted and processed files
      const englishPath = `2translate/toTranslate/${this.testData.fileName}`;
      const frenchPath = `2translate/toTranslate/${this.testData.fileName.replace('_eng.txt', '_other.txt')}`;

      console.log(`${COLORS.DEBUG}🔍 [Production Test] Checking processed files: ${englishPath}${COLORS.RESET}`);

      if (!fs.existsSync(englishPath)) {
        throw new Error(`Processed English subtitle file not found: ${englishPath}`);
      }

      const englishContent = fs.readFileSync(englishPath, 'utf8');
      const englishLines = englishContent.split('\n').filter(line => line.trim());

      console.log(`${COLORS.INFO}📊 [Production Test] English lines extracted: ${englishLines.length}${COLORS.RESET}`);
      console.log(`${COLORS.INFO}� [Production Test] Sample English line: ${englishLines[0]?.substring(0, 80)}...${COLORS.RESET}`);

      // Check for French subtitles
      let frenchLines = [];
      if (fs.existsSync(frenchPath)) {
        const frenchContent = fs.readFileSync(frenchPath, 'utf8');
        frenchLines = frenchContent.split('\n').filter(line => line.trim());
        console.log(`${COLORS.INFO}📊 [Production Test] French lines extracted: ${frenchLines.length}${COLORS.RESET}`);
        console.log(`${COLORS.INFO}📋 [Production Test] Sample French line: ${frenchLines[0]?.substring(0, 80)}...${COLORS.RESET}`);
      } else {
        console.log(`${COLORS.WARNING}⚠️  [Production Test] No French subtitles available${COLORS.RESET}`);
      }

      // Update sample lines based on actual content
      this.testData.sampleLines = Math.min(this.testData.sampleLines, englishLines.length);
      console.log(`${COLORS.INFO}📏 [Production Test] Adjusted sample lines to: ${this.testData.sampleLines}${COLORS.RESET}`);

      if (englishLines.length > 0) {
        this.testResults.subtitleExtraction = true;
        console.log(`${COLORS.SUCCESS}✅ [Production Test] Real subtitle extraction successful${COLORS.RESET}`);
      } else {
        throw new Error('No dialogue lines extracted from subtitles');
      }

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Production Test] Real subtitle extraction failed: ${error.message}${COLORS.RESET}`);
      this.captureDetailedError('subtitleExtraction', error, {
        mkvFile: this.testData.actualMkvFile,
        mkvPath: this.testData.mkvPath,
        expectedOutputs: [
          `1clear/extracted/${this.testData.actualMkvFile.slice(0, -4)}_eng.ass`,
          `2translate/toTranslate/${this.testData.fileName}`
        ]
      });
      this.testResults.subtitleExtraction = false;
    }
  }

  /**
   * Test complete translation cycle - the real deal
   */
  async testCompleteTranslationCycle() {
    console.log(`${COLORS.TEST}🔄 [Production Test] Testing COMPLETE TRANSLATION CYCLE (real translation)...${COLORS.RESET}`);
    console.log(`${COLORS.WARNING}⚠️  [Production Test] This will perform actual Claude 4 API calls and translation operations${COLORS.RESET}`);

    try {
      // Initialize the translator with production settings
      const translator = new Claude4Translator({
        maxTokens: 8192,
        temperature: 1,
        enableScreenshots: true,
        enableCorrection: true,
        maxRetries: 5,
        sceneDetection: {
          minSceneLength: 4,
          maxSceneLength: 50,
          timingGapThreshold: 5000,
          speakerChangeWeight: 0.3,
          timingWeight: 0.4,
          contentWeight: 0.3,
          qualityThreshold: 0.3,
          enforceMaxLength: true,
          enforceMinLength: true,
          fallbackSplitThreshold: 70
        },
        secondLanguageValidation: {
          enableValidation: true,
          validationThreshold: 0.6,
          secondLanguageDirectory: '2translate/toTranslate'
        },
        examplesReference: {
          enableExampleLookup: true,
          examplesPath: '2translate/examples.xml',
          maxRelevantExamples: 3
        },
        metadataPersistence: {
          enablePersistence: true,
          metadataDirectory: '2translate/metadata',
          autoSave: true
        }
      });

      const englishPath = `2translate/toTranslate/${this.testData.fileName}`;
      const content = fs.readFileSync(englishPath, 'utf8');

      // Limit to first N lines for testing
      const lines = content.split('\n').slice(0, this.testData.sampleLines);
      const limitedContent = lines.join('\n');

      console.log(`${COLORS.INFO}📝 [Production Test] Starting translation of ${lines.length} lines...${COLORS.RESET}`);
      console.log(`${COLORS.INFO}📋 [Production Test] Sample content: ${limitedContent.substring(0, 100)}...${COLORS.RESET}`);

      // Test scene detection first
      console.log(`${COLORS.INFO}🎬 [Production Test] Testing scene detection...${COLORS.RESET}`);
      const scenes = await translator.sceneDetector.detectScenes(limitedContent);
      console.log(`${COLORS.INFO}📊 [Production Test] Scenes detected: ${scenes.length}${COLORS.RESET}`);

      if (scenes.length > 0) {
        this.testResults.sceneDetection = true;
        console.log(`${COLORS.SUCCESS}✅ [Production Test] Scene detection successful${COLORS.RESET}`);
      } else {
        throw new Error('No scenes detected in content');
      }

      // Prepare metadata for translation
      const metadata = {
        title: this.testData.animeTitle,
        episode: this.testData.episode,
        characters: [
          { name: 'Protagonist', gender: 'male', personality: 'determined' },
          { name: 'Deuteragonist', gender: 'female', personality: 'supportive' },
          { name: 'Mentor', gender: 'male', personality: 'wise' },
          { name: 'Narrator', gender: 'neutral', personality: 'informative' }
        ],
        genres: ['Adventure', 'Fantasy', 'Action'],
        setting: 'magical world',
        tone: 'heroic'
      };

      console.log(`${COLORS.INFO}� [Production Test] Starting actual translation with Claude 4...${COLORS.RESET}`);

      // Perform the actual translation
      const translatedContent = await translator.translateSubtitles(
        limitedContent,
        null, // No video path for test
        metadata,
        this.testData.fileName
      );

      if (translatedContent && translatedContent.length > 0) {
        const translatedLines = translatedContent.split('\n').filter(line => line.trim());
        console.log(`${COLORS.INFO}📊 [Production Test] Translation completed: ${translatedLines.length} lines${COLORS.RESET}`);
        console.log(`${COLORS.INFO}📋 [Production Test] Sample translation: ${translatedLines[0]?.substring(0, 80)}...${COLORS.RESET}`);

        // Save the translated output for inspection
        const outputPath = `production-test-output-${Date.now()}.txt`;
        fs.writeFileSync(outputPath, translatedContent);
        console.log(`${COLORS.INFO}💾 [Production Test] Translation saved to: ${outputPath}${COLORS.RESET}`);

        this.testResults.translation = true;
        console.log(`${COLORS.SUCCESS}✅ [Production Test] Complete translation cycle successful${COLORS.RESET}`);
      } else {
        throw new Error('Translation returned empty content');
      }

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Production Test] Complete translation cycle failed: ${error.message}${COLORS.RESET}`);
      this.captureDetailedError('translation', error, {
        fileName: this.testData.fileName,
        sampleLines: this.testData.sampleLines,
        phase: 'complete_cycle'
      });
      this.testResults.translation = false;
      this.testResults.sceneDetection = false; // Also mark scene detection as failed
    }
  }



  /**
   * Test second language validation
   */
  async testSecondLanguageValidation() {
    console.log(`${COLORS.TEST}🔍 [Production Test] Testing second language validation...${COLORS.RESET}`);

    try {
      const validator = new SecondLanguageValidator({
        enableValidation: true,
        validationThreshold: 0.6,
        secondLanguageDirectory: '2translate/toTranslate'
      });

      // Test with sample translation
      const originalText = "Welcome to the world of magic and adventure.";
      const polishTranslation = "Witamy w świecie magii i przygód.";

      console.log(`${COLORS.INFO}📝 [Production Test] Testing validation with: "${originalText}" -> "${polishTranslation}"${COLORS.RESET}`);

      const result = await validator.validateTranslation(originalText, polishTranslation, {
        fileName: this.testData.fileName,
        animeTitle: this.testData.animeTitle,
        episode: this.testData.episode,
        sceneType: 'narrative'
      });

      console.log(`${COLORS.INFO}📊 [Production Test] Validation result: Valid=${result.isValid}, Confidence=${result.confidence?.toFixed(2)}${COLORS.RESET}`);
      console.log(`${COLORS.INFO}📋 [Production Test] Used second language: ${result.usedSecondLanguage}${COLORS.RESET}`);

      if (result.issues && result.issues.length > 0) {
        console.log(`${COLORS.WARNING}⚠️  [Production Test] Issues found: ${result.issues.join(', ')}${COLORS.RESET}`);
      }

      if (result.error) {
        console.log(`${COLORS.WARNING}⚠️  [Production Test] Validation error: ${result.error}${COLORS.RESET}`);
      }

      if (result.confidence !== undefined) {
        this.testResults.secondLanguageValidation = true;
        console.log(`${COLORS.SUCCESS}✅ [Production Test] Second language validation successful${COLORS.RESET}`);
      } else {
        throw new Error('Validation returned undefined confidence');
      }

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Production Test] Second language validation failed: ${error.message}${COLORS.RESET}`);
      this.captureDetailedError('secondLanguageValidation', error, {
        testText: "Welcome to the world of magic and adventure.",
        testTranslation: "Witamy w świecie magii i przygód.",
        fileName: this.testData.fileName
      });
      this.testResults.secondLanguageValidation = false;
    }
  }

  /**
   * Test examples reference system
   */
  async testExamplesReference() {
    console.log(`${COLORS.TEST}📚 [Production Test] Testing examples reference system...${COLORS.RESET}`);

    try {
      const examplesRef = new ExamplesReference({
        enableExampleLookup: true,
        examplesPath: '2translate/examples.xml',
        maxRelevantExamples: 3
      });

      // Test finding examples
      const queryText = "Thank you very much!";
      console.log(`${COLORS.INFO}📝 [Production Test] Searching for examples with query: "${queryText}"${COLORS.RESET}`);

      const result = await examplesRef.findRelevantExamples(queryText, "", {
        animeTitle: this.testData.animeTitle,
        episode: this.testData.episode
      });

      console.log(`${COLORS.INFO}📊 [Production Test] Examples found: ${result.relevantExamples.length}${COLORS.RESET}`);
      console.log(`${COLORS.INFO}📋 [Production Test] Has examples: ${result.hasExamples}${COLORS.RESET}`);

      if (result.error) {
        console.log(`${COLORS.WARNING}⚠️  [Production Test] Examples lookup error: ${result.error}${COLORS.RESET}`);
      }

      if (result.relevantExamples.length > 0) {
        console.log(`${COLORS.INFO}📖 [Production Test] Sample example: ${result.relevantExamples[0].englishSource} -> ${result.relevantExamples[0].idealOutput}${COLORS.RESET}`);
      }

      // Get statistics
      const stats = examplesRef.getStatistics();
      console.log(`${COLORS.INFO}📈 [Production Test] Total examples in database: ${stats.totalExamples}${COLORS.RESET}`);
      console.log(`${COLORS.INFO}📈 [Production Test] Indexed terms: ${stats.indexedTerms}${COLORS.RESET}`);

      if (stats.totalExamples >= 0) { // Even 0 examples is a valid result
        this.testResults.examplesReference = true;
        console.log(`${COLORS.SUCCESS}✅ [Production Test] Examples reference system successful${COLORS.RESET}`);
      } else {
        throw new Error('Examples reference returned invalid statistics');
      }

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Production Test] Examples reference failed: ${error.message}${COLORS.RESET}`);
      this.captureDetailedError('examplesReference', error, {
        queryText: "Thank you very much!",
        examplesPath: '2translate/examples.xml'
      });
      this.testResults.examplesReference = false;
    }
  }

  /**
   * Test metadata persistence system
   */
  async testMetadataPersistence() {
    console.log(`${COLORS.TEST}💾 [Production Test] Testing metadata persistence system...${COLORS.RESET}`);

    try {
      const metadata = new MetadataPersistence({
        enablePersistence: true,
        metadataDirectory: '2translate/metadata',
        autoSave: false // Disable auto-save for testing
      });

      console.log(`${COLORS.INFO}📝 [Production Test] Creating anime metadata for: ${this.testData.animeTitle}${COLORS.RESET}`);

      // Test anime metadata
      const animeData = metadata.getAnimeMetadata(this.testData.animeTitle, {
        genres: ["Adventure", "Fantasy"],
        totalEpisodes: 12,
        year: 2024,
        studio: "Test Studio"
      });

      console.log(`${COLORS.INFO}📊 [Production Test] Anime metadata created: ${animeData.title}${COLORS.RESET}`);
      console.log(`${COLORS.INFO}📋 [Production Test] Genres: ${animeData.genres?.join(', ')}${COLORS.RESET}`);

      // Test episode data
      console.log(`${COLORS.INFO}📝 [Production Test] Adding episode data for episode ${this.testData.episode}${COLORS.RESET}`);
      metadata.addEpisodeData(this.testData.animeTitle, this.testData.episode, {
        scenes: 5,
        lines: this.testData.sampleLines,
        quality: 0.85,
        characters: ["Protagonist", "Deuteragonist", "Mentor"],
        translatedAt: new Date().toISOString(),
        duration: "24:00"
      });

      // Test character info
      console.log(`${COLORS.INFO}📝 [Production Test] Updating character information...${COLORS.RESET}`);
      metadata.updateCharacterInfo(this.testData.animeTitle, {
        "Protagonist": {
          gender: "male",
          personality: "determined",
          speechPattern: "casual",
          voiceActor: "Test Actor"
        },
        "Deuteragonist": {
          gender: "female",
          personality: "supportive",
          speechPattern: "formal"
        }
      });

      // Get statistics
      const stats = metadata.getStatistics();
      console.log(`${COLORS.INFO}📈 [Production Test] Metadata statistics: ${stats.animeCount} anime, ${stats.totalEpisodes} episodes${COLORS.RESET}`);
      console.log(`${COLORS.INFO}📈 [Production Test] Character count: ${stats.characterCount || 0}${COLORS.RESET}`);

      if (stats.animeCount > 0) {
        this.testResults.metadataPersistence = true;
        console.log(`${COLORS.SUCCESS}✅ [Production Test] Metadata persistence successful${COLORS.RESET}`);
      } else {
        throw new Error('Metadata persistence returned no anime count');
      }

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Production Test] Metadata persistence failed: ${error.message}${COLORS.RESET}`);
      this.captureDetailedError('metadataPersistence', error, {
        animeTitle: this.testData.animeTitle,
        episode: this.testData.episode,
        metadataDirectory: '2translate/metadata'
      });
      this.testResults.metadataPersistence = false;
    }
  }

  /**
   * Generate error summary for the report
   */
  generateErrorSummary() {
    const summary = {};
    let totalErrors = 0;

    for (const [testName, errors] of Object.entries(this.detailedErrors)) {
      if (errors && errors.length > 0) {
        summary[testName] = {
          errorCount: errors.length,
          errorTypes: [...new Set(errors.map(e => e?.type || 'Unknown'))],
          latestError: errors[errors.length - 1]?.message || 'Unknown error',
          timestamp: errors[errors.length - 1]?.timestamp || new Date().toISOString()
        };
        totalErrors += errors.length;
      }
    }

    return {
      totalErrors,
      failedTests: Object.keys(summary).length,
      testDetails: summary
    };
  }

  /**
   * Generate comprehensive test report
   */
  async generateReport() {
    console.log(`${COLORS.PRODUCTION}📊 [Production Test] Generating comprehensive test report...${COLORS.RESET}`);

    const endTime = Date.now();
    const duration = (endTime - this.startTime) / 1000;

    // Calculate overall success
    const passedTests = Object.values(this.testResults).filter(result => result === true).length;
    const totalTests = Object.keys(this.testResults).length - 1; // Exclude overallSuccess
    this.testResults.overallSuccess = passedTests === totalTests;

    // Generate report with detailed error information
    const report = {
      testSuite: "Production Test Suite - Real World Scenario (Complete Translation Cycle)",
      timestamp: new Date().toISOString(),
      duration: `${duration.toFixed(2)} seconds`,
      testData: this.testData,
      results: {
        passed: passedTests,
        total: totalTests,
        success: this.testResults.overallSuccess,
        details: this.testResults
      },
      detailedErrors: this.detailedErrors,
      errorSummary: this.generateErrorSummary(),
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        architecture: process.arch
      }
    };

    // Display report
    console.log(`\n${COLORS.PRODUCTION}═══════════════════════════════════════════════════════════════${COLORS.RESET}`);
    console.log(`${COLORS.PRODUCTION}                    PRODUCTION TEST REPORT                      ${COLORS.RESET}`);
    console.log(`${COLORS.PRODUCTION}═══════════════════════════════════════════════════════════════${COLORS.RESET}`);

    console.log(`${COLORS.INFO}📋 Test Suite: ${report.testSuite}${COLORS.RESET}`);
    console.log(`${COLORS.INFO}⏰ Duration: ${report.duration}${COLORS.RESET}`);
    console.log(`${COLORS.INFO}📊 Results: ${report.results.passed}/${report.results.total} tests passed${COLORS.RESET}`);

    const statusColor = report.results.success ? COLORS.SUCCESS : COLORS.ERROR;
    const statusIcon = report.results.success ? '✅' : '❌';
    console.log(`${statusColor}${statusIcon} Overall Status: ${report.results.success ? 'PASSED' : 'FAILED'}${COLORS.RESET}`);

    console.log(`\n${COLORS.INFO}📝 Test Details:${COLORS.RESET}`);

    const testNames = {
      subtitleExtraction: 'Subtitle Extraction (Real File Operations)',
      sceneDetection: 'Scene Detection & Context Analysis (via Complete Cycle)',
      translation: 'Complete Translation Cycle (Real Claude 4 API)',
      secondLanguageValidation: 'Second Language Validation Tool',
      examplesReference: 'Examples Reference System',
      metadataPersistence: 'Metadata Persistence System'
    };

    for (const [key, name] of Object.entries(testNames)) {
      const result = this.testResults[key];
      const icon = result ? '✅' : '❌';
      const color = result ? COLORS.SUCCESS : COLORS.ERROR;
      console.log(`  ${color}${icon} ${name}${COLORS.RESET}`);
    }

    console.log(`\n${COLORS.INFO}🎯 Test Configuration:${COLORS.RESET}`);
    console.log(`  - Anime: ${this.testData.animeTitle}`);
    console.log(`  - Episode: ${this.testData.episode}`);
    console.log(`  - Lines tested: ${this.testData.sampleLines}`);
    console.log(`  - File: ${this.testData.fileName}`);

    // Display detailed error information if any errors occurred
    if (report.errorSummary.totalErrors > 0) {
      console.log(`\n${COLORS.ERROR}💥 Detailed Error Analysis:${COLORS.RESET}`);
      console.log(`${COLORS.ERROR}  - Total errors: ${report.errorSummary.totalErrors}${COLORS.RESET}`);
      console.log(`${COLORS.ERROR}  - Failed tests: ${report.errorSummary.failedTests}${COLORS.RESET}`);

      for (const [testName, errorInfo] of Object.entries(report.errorSummary.testDetails)) {
        console.log(`\n${COLORS.ERROR}  📋 ${testName}:${COLORS.RESET}`);
        console.log(`${COLORS.ERROR}    - Error count: ${errorInfo.errorCount}${COLORS.RESET}`);
        console.log(`${COLORS.ERROR}    - Error types: ${errorInfo.errorTypes.join(', ')}${COLORS.RESET}`);
        console.log(`${COLORS.ERROR}    - Latest error: ${errorInfo.latestError}${COLORS.RESET}`);
        console.log(`${COLORS.GRAY}    - Timestamp: ${errorInfo.timestamp}${COLORS.RESET}`);
      }
    }

    console.log(`\n${COLORS.INFO}🖥️  Environment:${COLORS.RESET}`);
    console.log(`  - Node.js: ${report.environment.nodeVersion}`);
    console.log(`  - Platform: ${report.environment.platform}`);
    console.log(`  - Architecture: ${report.environment.architecture}`);

    // Save report to file
    const reportPath = `app/production-test-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n${COLORS.INFO}💾 Report saved to: ${reportPath}${COLORS.RESET}`);

    console.log(`${COLORS.PRODUCTION}═══════════════════════════════════════════════════════════════${COLORS.RESET}\n`);

    if (report.results.success) {
      console.log(`${COLORS.SUCCESS}🎉 [Production Test] COMPLETE TRANSLATION CYCLE SUCCESSFUL! All systems operational and ready for production use.${COLORS.RESET}`);
      console.log(`${COLORS.SUCCESS}✨ [Production Test] The translation system successfully processed ${this.testData.sampleLines} lines with all tools working correctly.${COLORS.RESET}`);
    } else {
      console.log(`${COLORS.ERROR}⚠️  [Production Test] TRANSLATION CYCLE FAILED! Some systems encountered errors.${COLORS.RESET}`);
      console.log(`${COLORS.ERROR}🔧 [Production Test] Review the detailed error analysis above and fix issues before production deployment.${COLORS.RESET}`);
      if (report.errorSummary.totalErrors > 0) {
        console.log(`${COLORS.ERROR}📊 [Production Test] Total errors encountered: ${report.errorSummary.totalErrors} across ${report.errorSummary.failedTests} test(s)${COLORS.RESET}`);
      }
    }
  }

  /**
   * Cleanup test environment
   */
  async cleanup() {
    console.log(`${COLORS.INFO}🧹 [Production Test] Cleaning up test environment...${COLORS.RESET}`);

    try {
      // Remove test files
      const testFiles = [
        `app/2translate/toTranslate/${this.testData.fileName}`,
        `app/2translate/toTranslate/${this.testData.fileName.replace('_eng.txt', '_other.txt')}`
      ];

      for (const file of testFiles) {
        if (fs.existsSync(file)) {
          fs.unlinkSync(file);
        }
      }

      console.log(`${COLORS.SUCCESS}✅ [Production Test] Cleanup completed${COLORS.RESET}`);

    } catch (error) {
      console.warn(`${COLORS.WARNING}⚠️  [Production Test] Cleanup warning: ${error.message}${COLORS.RESET}`);
    }
  }
}

// Main execution
async function main() {
  const productionTest = new ProductionTest();
  await productionTest.runProductionTest();

  // Exit with appropriate code
  const success = productionTest.testResults.overallSuccess;
  process.exit(success ? 0 : 1);
}

// Run the production test
main().catch(error => {
  console.error(`${COLORS.ERROR}💥 [Production Test] Fatal error: ${error.message}${COLORS.RESET}`);
  console.error(error.stack);
  process.exit(1);
});
